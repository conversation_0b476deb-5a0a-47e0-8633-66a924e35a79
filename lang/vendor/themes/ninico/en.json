{"(:count reviews)": "(:count reviews)", "(:count)": "(:count)", "(Shipping fees not included)": "(Shipping fees not included)", "- (dash)": "- (dash)", "-- None --": "-- None --", "-- Select --": "-- Select --", "1 Review": "1 Review", "404 - Not found": "404 - Not found", "404 Not found icon": "404 Not found icon", "404 Page Not Found": "404 Page Not Found", "404. Page not found": "404. Page not found", "500 Internal Server Error": "500 Internal Server Error", "503 Service Unavailable": "503 Service Unavailable", ":count Items": ":count Items", ":count Reviews": ":count Reviews", ":count decrease": ":count decrease", ":count increase": ":count increase", ":count more": ":count more", ":customer has requested return product(s)": ":customer has requested return product(s)", ":name doesn't support :currency. List of currencies supported by :name: :currencies.": ":name doesn't support :currency. List of currencies supported by :name: :currencies.", ":name font family": ":name font family", ":name font size": ":name font size", ":number Star": ":number Star", ":number Stars": ":number Stars", ":number product available": ":number product available", ":number products available": ":number products available", ":percent% off": ":percent% off", ":product is already in your compare list!": ":product is already in your compare list!", ":qty x": ":qty x", ":total Product found": ":total Product found", ":total Products found": ":total Products found", ":total review(s) \":star star\" for \":product\"": ":total review(s) \":star star\" for \":product\"", ":total review(s) for \":product\"": ":total review(s) for \":product\"", "A new version (:version / released on :date) is available to update!": "A new version (:version / released on :date) is available to update!", "API Key": "API Key", "About": "About", "Accept and install": "Accept and install", "Account": "Account", "Account Dashboard": "Account Dashboard", "Account Menu": "Account <PERSON><PERSON>", "Account Settings": "Account <PERSON><PERSON>", "Account information": "Account information", "Action": "Action", "Actions": "Actions", "Ad :number": "Ad :number", "Add Another Address": "Add Another Address", "Add Google Maps iframe": "Add Google Maps iframe", "Add To Cart": "Add To Cart", "Add Wishlist": "Add Wishlist", "Add YouTube video": "Add YouTube video", "Add Your First Address": "Add Your First Address", "Add a custom menu to your widget area.": "Add a custom menu to your widget area.", "Add a new address": "Add a new address", "Add custom HTML content": "Add custom HTML content", "Add multiple addresses for different shipping locations or billing purposes.": "Add multiple addresses for different shipping locations or billing purposes.", "Add new": "Add new", "Add new address...": "Add new address...", "Add to Cart": "Add to Cart", "Add to cart": "Add to cart", "Add to compare": "Add to compare", "Add to wishlist": "Add to wishlist", "Add your first shipping or billing address to get started.": "Add your first shipping or billing address to get started.", "Add your review": "Add your review", "Added product :product successfully!": "Added product :product successfully!", "Added product :product to cart successfully!": "Added product :product to cart successfully!", "Added product :product to compare list successfully!": "Added product :product to compare list successfully!", "Added review successfully!": "Added review successfully!", "Address": "Address", "Address appears to be incomplete": "Address appears to be incomplete", "Address books": "Address books", "Addresses": "Addresses", "Admin": "Admin", "Ads": "Ads", "Ads :number": "Ads :number", "After creating the webhook, Razorpay will generate a <strong>Webhook Secret</strong>. Copy this secret and paste it into the <strong>Webhook Secret</strong> field in the settings form. This is required for secure webhook verification.": "After creating the webhook, <PERSON><PERSON><PERSON><PERSON> will generate a <strong>Webhook Secret</strong>. Copy this secret and paste it into the <strong>Webhook Secret</strong> field in the settings form. This is required for secure webhook verification.", "After registration at :name, you will have API key": "After registration at :name, you will have API key", "After registration at :name, you will have Client ID, Client Secret": "After registration at :name, you will have Client ID, Client Secret", "After registration at :name, you will have Public & Secret keys": "After registration at :name, you will have Public & Secret keys", "After registration at :name, you will have Store ID and Store Password (API/Secret key)": "After registration at :name, you will have Store ID and Store Password (API/Secret key)", "After the first image": "After the first image", "Agree terms and policy": "Agree terms and policy", "All": "All", "All Blog": "All Blog", "All Categories": "All Categories", "All Pages": "All Pages", "All categories": "All categories", "All caught up!": "All caught up!", "All files": "All files", "Already have an account?": "Already have an account?", "Amount": "Amount", "An error occurred while trying to login": "An error occurred while trying to login", "Android image": "Android image", "Android link": "Android link", "Apple token invalid": "Apple token invalid", "Applied coupon \":code\" successfully!": "Applied coupon \":code\" successfully!", "Apply": "Apply", "Apply Coupon": "Apply Coupon", "Are you sure you have received your order? Please confirm that the package has been delivered to you?": "Are you sure you have received your order? Please confirm that the package has been delivered to you?", "Are you sure you want to delete this address?": "Are you sure you want to delete this address?", "Are you sure you want to delete your review?": "Are you sure you want to delete your review?", "Are you sure you want to turn off the debug mode? This action cannot be undone.": "Are you sure you want to turn off the debug mode? This action cannot be undone.", "Are you sure?": "Are you sure?", "At <strong>Active Events</strong> field, make sure to enable the following events:": "At <strong>Active Events</strong> field, make sure to enable the following events:", "Audio File": "Audio File", "Availability": "Availability", "Available": "Available", "Back To Home": "Back To Home", "Back to cart": "Back to cart", "Back to login page": "Back to login page", "Back to shopping": "Back to shopping", "Background color": "Background color", "Background color (optional)": "Background color (optional)", "Background image": "Background image", "Background image (optional)": "Background image (optional)", "Bank transfer amount: <strong>:amount</strong>": "Bank transfer amount: <strong>:amount</strong>", "Bank transfer description: <strong>Payment for order :code</strong>": "Bank transfer description: <strong>Payment for order :code</strong>", "Banner": "Banner", "Banner image (1920x200px)": "Banner image (1920x200px)", "Barcode \":value\" has been duplicated on row #:row": "Barcode \":value\" has been duplicated on row #:row", "Before the last image": "Before the last image", "Below :toPrice": "Below :toP<PERSON>", "Billing information": "Billing information", "Blank (without header and footer)": "Blank (without header and footer)", "Blog Categories": "Blog Categories", "Blog Posts": "Blog Posts", "Blog Search": "Blog Search", "Blog Tags": "Blog Tags", "Blog sidebar": "Blog sidebar", "Body attributes": "Body attributes", "Bottom": "Bottom", "Brand:": "Brand:", "Brands": "Brands", "Breadcrumb background": "Breadcrumb background", "Breadcrumb background color": "Breadcrumb background color", "Browse Products": "Browse Products", "Button URL": "Button URL", "Button URL :number": "Button URL :number", "Button icon :number": "Button icon :number", "Button label": "Button label", "Button label :number": "Button label :number", "Buy Now": "Buy Now", "Buy now at :price": "Buy now at :price", "Buy with special price": "Buy with special price", "By :name": "By :name", "COD (Cash On Delivery) payment method is not available for digital products only.": "COD (Cash On Delivery) payment method is not available for digital products only.", "CTA contact": "CTA contact", "Can't send message on this time, please try again later!": "Can't send message on this time, please try again later!", "Cancel": "Cancel", "Cancel Order": "Cancel Order", "Cancel order": "Cancel order", "Cancel return order with reason: :reason": "Cancel return order with reason: :reason", "Cancellation Reason": "Cancellation Reason", "Cannot download files": "Cannot download files", "Cannot find this customer!": "Cannot find this customer!", "Cannot found files": "Cannot found files", "Cannot login, no email or Apple ID provided!": "Cannot login, no email or Apple ID provided!", "Cannot login, no email or Facebook ID provided!": "Cannot login, no email or Facebook ID provided!", "Cannot login, no email or Google ID provided!": "Cannot login, no email or Google ID provided!", "Cannot login, no email provided!": "Cannot login, no email provided!", "Captcha": "<PERSON><PERSON>", "Captcha Verification Failed!": "Captcha Verification Failed!", "Card color": "Card color", "Cart": "<PERSON><PERSON>", "Cart footer description": "Cart footer description", "Cart is empty": "Cart is empty", "Cart item is not existed!": "Cart item is not existed!", "Cart item not found": "Cart item not found", "Cart item removed successfully": "Cart item removed successfully", "Cart totals": "Cart totals", "Categories": "Categories", "Categories:": "Categories:", "Center Video": "Center Video", "Centered logo": "Centered logo", "Change Password": "Change Password", "Change avatar": "Change avatar", "Change copyright": "Change copyright", "Change password": "Change password", "Checkout": "Checkout", "Checkout error!": "Checkout error!", "Checkout successfully!": "Checkout successfully!", "Choose Reason": "Choose <PERSON>", "Choose a Reason for Order Cancellation": "Choose a Reason for Order Cancellation", "Choose a reason...": "Choose a reason...", "Choose categories": "Choose categories", "Choose collections": "Choose collections", "Choose date format for your front theme.": "Choose date format for your front theme.", "Choose products": "Choose products", "Choose teams": "Choose teams", "City": "City", "Close": "Close", "Collapsed": "Collapsed", "Collections": "Collections", "Color": "Color", "Coming Soon": "Coming Soon", "Comment": "Comment", "Company address": "Company address", "Company email": "Company email", "Company name": "Company name", "Company tax code": "Company tax code", "Compare": "Compare", "Compare Products": "Compare Products", "Completed at": "Completed at", "Confirm": "Confirm", "Confirm Delivery": "Confirm Delivery", "Confirm password": "Confirm password", "Confirm your password": "Confirm your password", "Contact Box": "Contact Box", "Contact Box (deprecated)": "Contact Box (deprecated)", "Content": "Content", "Continue Shopping": "Continue Shopping", "Continue shopping": "Continue shopping", "Copy link": "Copy link", "Copyright": "Copyright", "Copyright on footer of site": "Copyright on footer of site", "Copyright on footer of site. Using %Y to display current year.": "Copyright on footer of site. Using %Y to display current year.", "Copyright text at the bottom footer.": "Copyright text at the bottom footer.", "Cosmetics": "Cosmetics", "Cosmetics (random product)": "Cosmetics (random product)", "Could not download updated file. Please check your license or your internet network.": "Could not download updated file. Please check your license or your internet network.", "Could not update files & database.": "Could not update files & database.", "Country": "Country", "Coupon": "Coupon", "Coupon code": "Coupon code", "Coupon code discount amount": "Coupon code discount amount", "Coupon code is not valid or does not apply to the products": "Coupon code is not valid or does not apply to the products", "Coupon code: \":code\"": "Coupon code: \":code\"", "Coupon code: :code": "Coupon code: :code", "Coupon codes (:count)": "Coupon codes (:count)", "Create": "Create", "Create Address": "Create Address", "Created shipment for order": "Created shipment for order", "Current password": "Current password", "Custom": "Custom", "Custom CSS (optional)": "Custom CSS (optional)", "Custom Fields": "Custom Fields", "Custom HTML": "Custom HTML", "Custom Menu": "Custom Menu", "Customer ID must be a number!": "Customer ID must be a number!", "Customer Recently Viewed Products": "Customer Recently Viewed Products", "Customer can buy product and pay directly using Visa, Credit card via :name": "Customer can buy product and pay directly using Visa, Credit card via :name", "Customer forgot password form": "Customer forgot password form", "Customer information": "Customer information", "Customer login form": "Customer login form", "Customer register form": "Customer register form", "Customer reset password form": "Customer reset password form", "Customer reviews": "Customer reviews", "Customize footer": "Customize footer", "Dark": "Dark", "Date": "Date", "Date Shipped": "Date Shipped", "Date format": "Date format", "Date of birth": "Date of birth", "Deal Product": "Deal Product", "Default": "<PERSON><PERSON><PERSON>", "Default product items layout": "Default product items layout", "Delete": "Delete", "Delete Account": "Delete Account", "Delete your account": "Delete your account", "Delete your review": "Delete your review", "Deleted review successfully!": "Deleted review successfully!", "Delivered": "Delivered", "Delivery Notes": "Delivery Notes", "Delivery Notes:": "Delivery Notes:", "Delivery confirmed successfully": "Delivery confirmed successfully", "Description": "Description", "Device ID is required!": "Device ID is required!", "Digital Product": "Digital Product", "Discount": "Discount", "Discount :amount": "Discount :amount", "Discount :percentage%": "Discount :percentage%", "Discount promotion": "Discount promotion", "Dismiss": "<PERSON><PERSON><PERSON>", "Display Newsletter form on sidebar": "Display Newsletter form on sidebar", "Display Site features on sidebar": "Display Site features on sidebar", "Display accepted payments image.": "Display accepted payments image.", "Display blog categories": "Display blog categories", "Display blog posts": "Display blog posts", "Display blog tags": "Display blog tags", "Display on pages": "Display on pages", "Display posts count?": "Display posts count?", "Display product categories on mobile menu?": "Display product categories on mobile menu?", "Display product categories select on header?": "Display product categories select on header?", "Display recent blog posts": "Display recent blog posts", "Do you really want to delete the review?": "Do you really want to delete the review?", "Documents": "Documents", "Don't have an account?": "Don't have an account?", "Don't show this popup again": "Don't show this popup again", "Download Apps": "Download Apps", "Download all files": "Download all files", "Download invoice": "Download invoice", "Download product \":name\" with external links": "Download product \":name\" with external links", "Downloaded": "Downloaded", "Downloads": "Downloads", "Edit": "Edit", "Edit Account": "Edit Account", "Edit Address #:id": "Edit Address #:id", "Edit this shortcode": "Edit this shortcode", "Edit this widget": "Edit this widget", "Edit your profile and account details": "Edit your profile and account details", "Either email or phone is required": "Either email or phone is required", "Email": "Email", "Email (optional)": "Email (optional)", "Email Address": "Email Address", "Email address": "Email address", "Email is required": "Email is required", "Email or Phone number": "Email or Phone number", "Email or phone": "Email or phone", "Empty cart successfully!": "Empty cart successfully!", "Enable Facebook comment in post detail page?": "Enable Facebook comment in post detail page?", "Enable Facebook comment in the product page?": "Enable Facebook comment in the product page?", "Enable Newsletter Popup": "Enable Newsletter Popup", "Enable Preloader?": "Enable Preloader?", "Enable bottom menu on mobile?": "Enable bottom menu on mobile?", "Enable dark mode": "Enable dark mode", "Enable lazy loading": "Enable lazy loading", "Enable light mode": "Enable light mode", "Enable quick view button?": "Enable quick view button?", "Enable sticky header on mobile?": "Enable sticky header on mobile?", "Enable sticky header?": "Enable sticky header?", "End date": "End date", "Ensure your account is using a long, random password to stay secure.": "Ensure your account is using a long, random password to stay secure.", "Enter API key into the box in right hand": "Enter API key into the box in right hand", "Enter Client ID, Secret into the box in right hand": "Enter Client ID, Secret into the box in right hand", "Enter Coupon Code": "Enter Coupon Code", "Enter Public, Secret into the box in right hand": "Enter Public, Secret into the box in right hand", "Enter Store ID and Store Password (API/Secret key) into the box in right hand": "Enter Store ID and Store Password (API/Secret key) into the box in right hand", "Enter Your Email": "Enter Your Email", "Enter coupon code...": "Enter coupon code...", "Enter keyword...": "Enter keyword...", "Enter the order ID": "Enter the order ID", "Enter your business email address where invoices and tax documents will be sent (e.g., <EMAIL>).": "Enter your business email address where invoices and tax documents will be sent (e.g., <EMAIL>).", "Enter your business tax identification number such as Tax ID, VAT number, or EIN (e.g., 12-3456789, VAT123456789, EIN 12-3456789).": "Enter your business tax identification number such as Tax ID, VAT number, or EIN (e.g., 12-3456789, VAT123456789, EIN 12-3456789).", "Enter your complete business address including street, city, state, and postal code (e.g., 123 Business Street, Suite 100, City, State 12345).": "Enter your complete business address including street, city, state, and postal code (e.g., 123 Business Street, Suite 100, City, State 12345).", "Enter your current password": "Enter your current password", "Enter your email": "Enter your email", "Enter your phone number": "Enter your phone number", "Enter your registered business or company name as it appears on official documents (e.g., ABC Corporation Ltd.).": "Enter your registered business or company name as it appears on official documents (e.g., ABC Corporation Ltd.).", "Error": "Error", "Error when processing payment via :paymentType!": "Error when processing payment via :paymentType!", "Estimate Date Shipped": "Estimate Date Shipped", "Ex: Shop Now": "Ex: Shop Now", "Exception": "Exception", "Explore and add items to get started": "Explore and add items to get started", "External link downloads": "External link downloads", "FAQs": "FAQs", "Facebook": "Facebook", "Facebook Admin ID": "Facebook Admin ID", "Facebook Admins": "Facebook Admins", "Facebook App ID": "Facebook App ID", "Facebook Integration": "Facebook Integration", "Facebook admins to manage comments :link": "Facebook admins to manage comments :link", "Facebook authentication is not properly configured": "Facebook authentication is not properly configured", "Facebook page ID": "Facebook page ID", "Facebook token invalid": "Facebook token invalid", "Failed to return the order": "Failed to return the order", "Fashion": "Fashion", "Featured": "Featured", "Featured Posts": "Featured Posts", "Features": "Features", "Filter": "Filter", "Filter Products": "Filter Products", "Fix it for me": "Fix it for me", "Flash sale": "Flash sale", "Footer background color": "Footer background color", "Footer border color": "Footer border color", "Footer bottom background color": "Footer bottom background color", "Footer bottom sidebar": "Footer bottom sidebar", "Footer logo": "Footer logo", "Footer middle sidebar": "Footer middle sidebar", "Footer sidebar": "Footer sidebar", "Footer text color": "Footer text color", "Footer text muted color": "Footer text muted color", "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.": "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.", "For devices with width less than 768px, if empty, will use the image from the tablet.": "For devices with width less than 768px, if empty, will use the image from the tablet.", "Forgot Password": "Forgot Password", "Forgot password?": "Forgot password?", "Form subtitle": "Form subtitle", "Form title": "Form title", "Free": "Free", "Free Shipping": "Free Shipping", "Free shipping": "Free shipping", "Free shipping for all orders": "Free shipping for all orders", "Free shipping to <strong>:target</strong>": "Free shipping to <strong>:target</strong>", "From": "From", "From :fromPrice to :toPrice": "From :fromPrice to :toPrice", "Full Name": "Full Name", "Full name": "Full name", "Full width": "Full width", "Full width style will only display the slider image without any text, action button or ads. The recommended image dimension is 1920x512 px.": "Full width style will only display the slider image without any text, action button or ads. The recommended image dimension is 1920x512 px.", "Functions": "Functions", "Furniture": "Furniture", "Galleries": "Galleries", "Get A Quote": "Get A Quote", "Get Newsletter": "Get Newsletter", "Get on the list and get 10% off your first order!": "Get on the list and get 10% off your first order!", "Get webhook secret key from your Razorpay dashboard to verify webhook requests": "Get webhook secret key from your Razorpay dashboard to verify webhook requests", "Gift": "Gift", "Go To Cart": "Go To Cart", "Go back home": "Go back home", "Go to :link to change the copyright text.": "Go to :link to change the copyright text.", "Google Maps": "Google Maps", "Google authentication is not properly configured": "Google authentication is not properly configured", "Google token invalid": "Google token invalid", "Grid": "Grid", "Grocery": "Grocery", "HTML attributes": "HTML attributes", "HTML code": "HTML code", "Has sidebar": "Has sidebar", "Header background color": "Header background color", "Header menu text color": "Header menu text color", "Header menu text hover color": "Header menu text hover color", "Header sidebar categories limit": "Header sidebar categories limit", "Header style": "Header style", "Header text color": "Header text color", "Height": "Height", "Highlight background image": "Highlight background image", "Home": "Home", "Homepage": "Homepage", "Horizontal": "Horizontal", "Hotline": "Hotline", "Hours": "Hours", "Humanitarian Donation": "Humanitarian Donation", "Hurry up! Sale end in": "Hurry up! Sale end in", "I agree to the :link": "I agree to the :link", "I agree to the Terms and Privacy Policy": "I agree to the Terms and Privacy Policy", "ID number": "ID number", "Icon": "Icon", "Icon :number": "Icon :number", "Icon Image (It will override icon above if set)": "Icon Image (It will override icon above if set)", "Icon image": "Icon image", "Icon image (It will override icon above if set)": "Icon image (It will override icon above if set)", "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.": "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.", "If you need help, contact us at :mail.": "If you need help, contact us at :mail.", "Image": "Image", "Image :number": "Image :number", "Images from customer (:count)": "Images from customer (:count)", "In Transit": "In Transit", "In stock": "In stock", "Inherit (use footer style from theme options)": "Inherit (use footer style from theme options)", "Inherit (use header style from theme options)": "Inherit (use header style from theme options)", "Install plugin from Marketplace": "Install plugin from Marketplace", "Internal Server Error": "Internal Server Error", "Invalid Apple token": "Invalid Apple token", "Invalid Data!": "Invalid Data!", "Invalid Facebook token": "Invalid Facebook token", "Invalid Google token": "Invalid Google token", "Invalid Transaction!": "Invalid Transaction!", "Invalid data send": "Invalid data send", "Invalid guard configuration": "Invalid guard configuration", "Invalid step.": "Invalid step.", "InvalidStateException occurred while trying to login": "InvalidStateException occurred while trying to login", "Invoice detail :code": "Invoice detail :code", "Invoices": "Invoices", "It is important to enable <strong>ALL</strong> these events to ensure your system captures all payment statuses correctly. Missing events may result in payments not being recorded in your system.": "It is important to enable <strong>ALL</strong> these events to ensure your system captures all payment statuses correctly. Missing events may result in payments not being recorded in your system.", "It looks as through there are no activities here.": "It looks as through there are no activities here.", "It will replace Icon Font if it is present.": "It will replace Icon Font if it is present.", "Items": "Items", "Items Count": "Items Count", "Items per view": "Items per view", "Key": "Key", "Last update": "Last update", "Latest": "Latest", "Lazy load images": "Lazy load images", "Lazy load placeholder image (250x250px)": "Lazy load placeholder image (250x250px)", "Learn more": "Learn more", "Learn more about Twig template: :url": "Learn more about Twig template: :url", "Leave blank to use flash sale name (shown on \"List products\" style)": "Leave blank to use flash sale name (shown on \"List products\" style)", "Leave categories empty if you want to show posts from all categories.": "Leave categories empty if you want to show posts from all categories.", "Leave empty to use the default logo in Theme Options.": "Leave empty to use the default logo in Theme Options.", "Leave it empty to use the default description from Theme options -> General.": "Leave it empty to use the default description from Theme options -> General.", "Left :left": "Left :left", "Left margin in LTR, right margin in RTL": "Left margin in LTR, right margin in RTL", "Left sidebar": "Left sidebar", "License": "License", "License Activation": "License Activation", "Light": "Light", "Limit": "Limit", "LinkedIn": "LinkedIn", "List": "List", "List all product categories": "List all product categories", "List item :number": "List item :number", "List item url :number": "List item url :number", "List products": "List products", "Load more": "Load more", "Log files": "Log files", "Login": "<PERSON><PERSON>", "Login / Register": "Login / Register", "Login successful": "Login successful", "Login to your account": "Login to your account", "Login with social networks": "Login with social networks", "Logo": "Logo", "Logo height (default: 35px)": "Logo height (default: 35px)", "Logo height (px)": "Logo height (px)", "Logo light": "Logo light", "Logo style": "Logo style", "Logout": "Logout", "Looks like there are no reviews yet.": "Looks like there are no reviews yet.", "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.": "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.", "Make Custom Request": "Make Custom Request", "Manage Addresses": "Manage Addresses", "Manage Invoices": "Manage Invoices", "Manage your account, view orders, and update your preferences from your personal dashboard.": "Manage your account, view orders, and update your preferences from your personal dashboard.", "Manage your shipping and billing addresses": "Manage your shipping and billing addresses", "Margin Bottom (px)": "<PERSON>gin Bottom (px)", "Margin End (px)": "<PERSON>gin End (px)", "Margin Start (px)": "<PERSON>gin Start (px)", "Margin Top (px)": "<PERSON>gin Top (px)", "Marque highlight URL": "<PERSON><PERSON> highlight URL", "Marque highlight text": "Marque highlight text", "Marque only works with cosmetics style.": "<PERSON><PERSON> only works with cosmetics style.", "Marque text": "Marque text", "Math Captcha": "<PERSON>", "Math Captcha Verification Failed!": "Math Captcha Verification Failed!", "Maximum order quantity is :qty, please check your cart and retry again!": "Maximum order quantity is :qty, please check your cart and retry again!", "Maximum order quantity of product :product is :quantity!": "Maximum order quantity of product :product is :quantity! ", "Maximum quantity is :max!": "Maximum quantity is :max!", "Media - Audio": "Media - Audio", "Media - Video": "Media - Video", "Media URL": "Media URL", "Menu": "<PERSON><PERSON>", "Merchandise": "Merchandise", "Message": "Message", "Minimum order amount is :amount, you need to buy more :more to place an order!": "Minimum order amount is :amount, you need to buy more :more to place an order!", "Minimum order amount to use :name payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use :name payment method is :amount, you need to buy more :more to place an order!", "Minimum order amount to use Bank Transfer payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use Bank Transfer payment method is :amount, you need to buy more :more to place an order!", "Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!", "Minimum order quantity is :qty, you need to buy more :more to place an order!": "Minimum order quantity is :qty, you need to buy more :more to place an order!", "Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order!": "Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order! ", "Minus": "Minus", "Mobile Image": "Mobile Image", "Moderator's note": "Moderator's note", "More Categories": "More Categories", "Must-have pieces selected every month want style Ideas and Treats?": "Must-have pieces selected every month want style Ideas and Treats?", "My Profile": "My Profile", "Name": "Name", "Name: A-Z": "Name: A-<PERSON>", "Name: Z-A": "Name: Z-<PERSON>", "Need another address?": "Need another address?", "New password": "New password", "Newest": "Newest", "Newsletter Popup": "Newsletter Popup", "Newsletter form": "Newsletter form", "Next": "Next", "No": "No", "No addresses yet!": "No addresses yet!", "No coupon code found": "No coupon code found", "No digital products!": "No digital products!", "No files found": "No files found", "No order return requests yet!": "No order return requests yet!", "No orders yet!": "No orders yet!", "No payment charge. Please try again!": "No payment charge. Please try again!", "No product in wishlist!": "No product in wishlist!", "No products found!": "No products found!", "No products found.": "No products found.", "No products in cart": "No products in cart", "No products in cart. :link!": "No products in cart. :link!", "No products in compare list!": "No products in compare list!", "No products pending review": "No products pending review", "No results found": "No results found", "No results found!": "No results found!", "No reviews yet!": "No reviews yet!", "No reviews!": "No reviews!", "No shipping methods were found with your provided shipping information!": "No shipping methods were found with your provided shipping information!", "Not available": "Not available", "Not available in COD payment option.": "Not available in COD payment option.", "Not specified": "Not specified", "Note": "Note", "Notes about your order, e.g. special notes for delivery.": "Notes about your order, e.g. special notes for delivery.", "Number of categories to display": "Number of categories to display", "Number of galleries to show. Set to 0 or leave it empty to show all. It will be overridden if you select galleries below.": "Number of galleries to show. Set to 0 or leave it empty to show all. It will be overridden if you select galleries below.", "Number of posts to display": "Number of posts to display", "Number of products per category": "Number of products per category", "Number of products per row": "Number of products per row", "Number of products per row on mobile": "Number of products per row on mobile", "Number of products to display": "Number of products to display", "Number tags to display": "Number tags to display", "Oldest": "Oldest", "On Sale": "On Sale", "On sale": "On sale", "One or all products are not enough quantity so cannot update!": "One or all products are not enough quantity so cannot update!", "Oops! Something Went Wrong.": "Oops! Something Went Wrong.", "Open user menu": "Open user menu", "Or you can upload a new one, the old one will be replaced.": "Or you can upload a new one, the old one will be replaced.", "Order": "Order", "Order Details": "Order Details", "Order ID": "Order ID", "Order ID number": "Order ID number", "Order Information": "Order Information", "Order Return Request not found!": "Order Return Request not found!", "Order Return Requests": "Order Return Requests", "Order Return Requests :id": "Order Return Requests :id", "Order Tracking": "Order Tracking", "Order cancelled successfully": "Order cancelled successfully", "Order code is required": "Order code is required", "Order completed": "Order completed", "Order detail :id": "Order detail :id", "Order found successfully": "Order found successfully", "Order information": "Order information", "Order is created from the checkout page": "Order is created from the checkout page", "Order not found": "Order not found", "Order not found!": "Order not found!", "Order notes": "Order notes", "Order number": "Order number", "Order returned successfully": "Order returned successfully", "Order status": "Order status", "Order successfully. Order number :id": "Order successfully. Order number :id", "Order tracking": "Order tracking", "Order tracking :code": "Order tracking :code", "Order tracking background": "Order tracking background", "Order tracking is not enabled": "Order tracking is not enabled", "Order was cancelled by customer :customer": "Order was cancelled by customer :customer", "Order was cancelled by customer :customer with reason :reason": "Order was cancelled by customer :customer with reason :reason", "Order was confirmed delivery by customer :customer": "Order was confirmed delivery by customer :customer", "Order was created from checkout page": "Order was created from checkout page", "Ordered at": "Ordered at", "Orders": "Orders", "Other": "Other", "Out of stock": "Out of stock", "Over :fromPrice": "Over :fromPrice", "Overview": "Overview", "PHP version :version required": "PHP version :version required", "Page could not be found": "Page could not be found", "Password": "Password", "Password confirmation": "Password confirmation", "Payment Method": "Payment Method", "Payment Proof": "Payment Proof", "Payment Type": "Payment Type", "Payment description": "Payment description", "Payment failed!": "Payment failed!", "Payment failed! Missing transaction ID.": "Payment failed! Missing transaction ID.", "Payment failed! Something wrong with your payment. Please try again.": "Payment failed! Something wrong with your payment. Please try again.", "Payment failed! Status: :status": "Payment failed! Status: :status", "Payment method": "Payment method", "Payment proof upload is currently disabled.": "Payment proof upload is currently disabled.", "Payment status": "Payment status", "Payment with :paymentType": "Payment with :paymentType", "Permanently delete your account and all associated data.": "Permanently delete your account and all associated data.", "Phone": "Phone", "Phone (optional)": "Phone (optional)", "Phone is required": "Phone is required", "Phone number": "Phone number", "Pinterest": "Pinterest", "Please <a href=\":link\">login</a> to write review!": "Please <a href=\":link\">login</a> to write review!", "Please agree to the terms and conditions before proceeding.": "Please agree to the terms and conditions before proceeding.", "Please enter a valid number for quantity": "Please enter a valid number for quantity", "Please enter the quantity you want to buy": "Please enter the quantity you want to buy", "Please enter your CSS code on a single line. It wont work if it has break line. Some special characters may be escaped.": "Please enter your CSS code on a single line. It wont work if it has break line. Some special characters may be escaped.", "Please fill out all shipping information to view available shipping methods!": "Please fill out all shipping information to view available shipping methods!", "Please provide a reason for the cancellation.": "Please provide a reason for the cancellation.", "Please purchase the product for a review!": "Please purchase the product for a review!", "Please select a product to add to cart": "Please select a product to add to cart", "Please select at least 1 product to return!": "Please select at least 1 product to return!", "Please select attributes": "Please select attributes", "Please select product options": "Please select product options", "Please select product options!": "Please select product options!", "Please solve the following math function: :label = ?": "Please solve the following math function: :label = ?", "Please switch currency to any supported currency": "Please switch currency to any supported currency", "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.": "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.", "Plus": "Plus", "Popular": "Popular", "Popular tags": "Popular tags", "Popup Delay (seconds)": "<PERSON><PERSON> (seconds)", "Popup Description": "Popup Description", "Popup Image": "Popup Image", "Popup Subtitle": "Popup Subtitle", "Popup Title": "Popup Title", "Post type": "Post type", "Preloader Version": "Preloader Version", "Prev": "Prev", "Price": "Price", "Price: high to low": "Price: high to low", "Price: low to high": "Price: low to high", "Primary": "Primary", "Primary color": "Primary color", "Primary font": "Primary font", "Print invoice": "Print invoice", "Proceed to Checkout": "Proceed to Checkout", "Processing. Please wait...": "Processing. Please wait...", "Product": "Product", "Product :product is out of stock!": "Product :product is out of stock!", "Product :product limited quantity allowed is :quantity": "Product :product limited quantity allowed is :quantity", "Product Categories": "Product Categories", "Product Collections": "Product Collections", "Product FAQs": "Product FAQs", "Product ID is required": "Product ID is required", "Product Reviews": "Product Reviews", "Product Specification": "Product Specification", "Product categories": "Product categories", "Product detail sidebar": "Product detail sidebar", "Product gallery image style": "Product gallery image style", "Product gallery image style?": "Product gallery image style?", "Product gallery video position": "Product gallery video position", "Product is not published yet.": "Product is not published yet.", "Product is out of stock": "Product is out of stock", "Product name \":name\" does not exists": "Product name \":name\" does not exists", "Product not found": "Product not found", "Product not found in compare list": "Product not found in compare list", "Product not found in wishlist": "Product not found in wishlist", "Product(s)": "Product(s)", "Products": "Products", "Products Slide": "Products Slide", "Products by Categories": "Products by Categories", "Products listing page layout": "Products listing page layout", "Products listing sidebar": "Products listing sidebar", "Products you have reviewed": "Products you have reviewed", "Profile": "Profile", "Profile Information": "Profile Information", "Promotion": "Promotion", "Promotion discount amount": "Promotion discount amount", "Public Key": "Public Key", "Quantity": "Quantity", "Quantity is required!": "Quantity is required!", "Quantity must be a number!": "Quantity must be a number!", "Questions & Answers": "Questions & Answers", "Quick view": "Quick view", "Random products": "Random products", "Rate this product:": "Rate this product:", "Rating": "Rating", "Rating: high to low": "Rating: high to low", "Rating: low to high": "Rating: low to high", "Ready to start shopping?": "Ready to start shopping?", "Reason": "Reason", "Reason (optional)": "Reason (optional)", "Recent": "Recent", "Recent Post": "Recent Post", "Recent Posts": "Recent Posts", "Redirecting to Razorpay...": "Redirecting to Razorpay...", "Refund amount": "Refund amount", "Register": "Register", "Register an account": "Register an account", "Register an account on :name": "Register an account on :name", "Register an account with above information?": "Register an account with above information?", "Register now": "Register now", "Registered successfully!": "Registered successfully!", "Related Products": "Related Products", "Related products": "Related products", "Remains until the <br> end of the offer": "Remains until the <br> end of the offer", "Remains until the end of the offer": "Remains until the end of the offer", "Remember me": "Remember me", "Remove": "Remove", "Remove image": "Remove image", "Removed coupon :code successfully!": "Removed coupon :code successfully!", "Removed coupon code successfully!": "Removed coupon code successfully!", "Removed item from cart successfully!": "Removed item from cart successfully!", "Removed product :product from compare list successfully!": "Removed product :product from compare list successfully!", "Removed product :product from wishlist successfully!": "Removed product :product from wishlist successfully!", "Removing...": "Removing...", "Request Return Product(s)": "Request Return Product(s)", "Request Return Product(s) In Order :id": "Request Return Product(s) In Order :id", "Request number": "Request number", "Request return order with reason: :reason": "Request return order with reason: :reason", "Requires company invoice (Please fill in your company information to receive the invoice)?": "Requires company invoice (Please fill in your company information to receive the invoice)?", "Reset Password": "Reset Password", "Return Product(s)": "Return Product(s)", "Return Reason": "Return Reason", "Return items": "Return items", "Returned Goods": "Returned Goods", "Returned to Sender": "Returned to <PERSON>er", "Review": "Review", "Review product \":product\"": "Review product \":product\"", "Reviews": "Reviews", "Reviews (:count)": "Reviews (:count)", "Reviews :number": "Reviews :number", "Right margin in LTR, left margin in RTL": "Right margin in LTR, left margin in RTL", "Right sidebar": "Right sidebar", "Run": "Run", "SKU": "SKU", "SKU:": "SKU:", "Same as shipping information": "Same as shipping information", "Same fee :amount": "Same fee :amount", "Search": "Search", "Search Products...": "Search Products...", "Search blog posts": "Search blog posts", "Search for Products...": "Search for Products...", "Search products...": "Search products...", "Search result for \":query\"": "Search result for \":query\"", "Search result for: \":query\"": "Search result for: \":query\"", "Search...": "Search...", "Secret": "Secret", "Secret Key": "Secret Key", "See all results": "See all results", "Select :name": "Select :name", "Select Coupon": "Select Coupon", "Select Options": "Select Options", "Select an option": "Select an option", "Select available addresses": "Select available addresses", "Select billing address...": "Select billing address...", "Select categories": "Select categories", "Select city...": "Select city...", "Select country...": "Select country...", "Select file": "Select file", "Select menu": "Select menu", "Select options": "Select options", "Select state...": "Select state...", "Send": "Send", "Send Password Reset Link": "Send Password Reset Link", "Send message successfully!": "Send message successfully!", "Services": "Services", "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.": "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.", "Set the height of the logo in pixels. The default value is 35px.": "Set the height of the logo in pixels. The default value is 35px.", "Set the height of the logo in pixels. The default value is :default.": "Set the height of the logo in pixels. The default value is :default.", "Settings in this section only works with style Grocery": "Settings in this section only works with style Grocery", "Setup license code": "Setup license code", "Share": "Share", "Share on :social": "Share on :social", "Share your experience with these products": "Share your experience with these products", "Share:": "Share:", "Shipment status": "Shipment status", "Shipping Address": "Shipping Address", "Shipping Company Name": "Shipping Company Name", "Shipping Information": "Shipping Information", "Shipping Label Created": "Shipping Label Created", "Shipping Status": "Shipping Status", "Shipping fee": "Shipping fee", "Shipping information": "Shipping information", "Shipping method": "Shipping method", "Shop Location": "Shop Location", "Shopping Cart": "Shopping Cart", "Show ads title": "Show ads title", "Show label for bottom menu item on mobile?": "Show label for bottom menu item on mobile?", "Show only discounted products": "Show only discounted products", "Show slider image on mobile": "Show slider image on mobile", "Show subscribe form?": "Show subscribe form?", "Site Accepted Payments": "Site Accepted Payments", "Site Copyright": "Site Copyright", "Site features": "Site features", "Site information": "Site information", "Slug": "Slug", "Social": "Social", "Social Links": "Social Links", "Social Links at the footer.": "Social Links at the footer.", "Social Sharing": "Social Sharing", "Social sharing buttons": "Social sharing buttons", "Sold by": "Sold by", "Sold: :count": "Sold: :count", "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.": "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.", "Something went wrong.": "Something went wrong.", "Something went wrong. Please try again": "Something went wrong. Please try again", "Sorry, we are doing some maintenance. Please check back soon.": "Sorry, we are doing some maintenance. Please check back soon.", "Sorry, we couldn’t find the page you where looking for. We suggest that <br> you return to homepage.": "Sorry, we couldn’t find the page you where looking for. We suggest that <br> you return to homepage.", "Sorry, you can only order a maximum of :quantity units of :product at a time. Please adjust the quantity and try again.": "Sorry, you can only order a maximum of :quantity units of :product at a time. Please adjust the quantity and try again.", "Sorry, you cannot buy more than 99,999 items at once": "Sorry, you cannot buy more than 99,999 items at once", "Specification": "Specification", "Start Shopping": "Start Shopping", "Start shopping now": "Start shopping now", "State": "State", "Status": "Status", "Stock status": "Stock status", "Store Hours:": "Store Hours:", "Store ID": "Store ID", "Store Password (API/Secret key)": "Store Password (API/Secret key)", "Store hours": "Store hours", "Story text :number": "Story text :number", "Style": "Style", "Style 1": "Style 1", "Style 2": "Style 2", "Styles": "Styles", "Subject": "Subject", "Submit": "Submit", "Submit Return Request": "Submit Return Request", "Submit Review": "Submit Review", "Subscribe": "Subscribe", "Subscribe Now": "Subscribe Now", "Subscribe to newsletter successfully!": "Subscribe to newsletter successfully!", "Subtitle": "Subtitle", "Subtotal": "Subtotal", "Subtotal:": "Subtotal:", "Success": "Success", "Support native audio": "Support native audio", "Support native video, YouTube, Vimeo, TikTok, X (Twitter)": "Support native video, YouTube, Vimeo, TikTok, X (Twitter)", "Tab #:number": "Tab #:number", "Tab :number": "Tab :number", "Tablet Image": "Tablet Image", "Tabs": "Tabs", "Tags": "Tags", "Tags:": "Tags:", "Take me home": "Take me home", "Tax": "Tax", "Tax ID:": "Tax ID:", "Tax information": "Tax information", "Tax:": "Tax:", "Team": "Team", "Teams": "Teams", "Telegram": "Telegram", "Tell us why you want to delete your account...": "Tell us why you want to delete your account...", "Temporarily down for maintenance": "Temporarily down for maintenance", "Term and Policy": "Term and Policy", "Terms and Privacy Policy": "Terms and Privacy Policy", "Testimonials": "Testimonials", "Text": "Text", "Text :number": "Text :number", "Text color": "Text color", "Text color (optional)": "Text color (optional)", "Thank you for purchasing our products!": "Thank you for purchasing our products!", "The .env file is not writable.": "The .env file is not writable.", "The debug mode has been disabled successfully.": "The debug mode has been disabled successfully.", "The debug mode is already disabled.": "The debug mode is already disabled.", "The font size in pixels (px). Default is :default": "The font size in pixels (px). Default is :default", "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>": "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>", "The order could not be found. Please try again or contact us if you need assistance.": "The order could not be found. Please try again or contact us if you need assistance.", "The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:": "The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:", "The page you are looking for could not be found.": "The page you are looking for could not be found.", "The selected :attribute is invalid.": "The selected :attribute is invalid.", "The system is up-to-date. There are no new versions to update!": "The system is up-to-date. There are no new versions to update!", "Theme Ads": "Theme Ads", "Theme built-in": "Theme built-in", "Theme options": "Theme options", "Then you need to create a new webhook. To create a webhook, go to <strong>Account Settings</strong>-><strong>API keys</strong>-><strong>Webhooks</strong> and paste the below url to <strong>Webhook URL</strong> field.": "Then you need to create a new webhook. To create a webhook, go to <strong>Account Set<PERSON>s</strong>-><strong>API keys</strong>-><strong>Webhooks</strong> and paste the below url to <strong>Webhook URL</strong> field.", "There is no data to display!": "There is no data to display!", "This action cannot be undone": "This action cannot be undone", "This action will permanently delete your account and all associated data and is irreversible. Please be sure before proceeding.": "This action will permanently delete your account and all associated data and is irreversible. Please be sure before proceeding.", "This color may be overridden by the theme. If it doesnt work, please add your CSS in Appearance -> Custom CSS.": "This color may be overridden by the theme. If it doesnt work, please add your CSS in Appearance -> Custom CSS.", "This credential is invalid Google Analytics credentials.": "This credential is invalid Google Analytics credentials.", "This feature is temporary disabled in demo mode. Please use another login option. Such as Google.": "This feature is temporary disabled in demo mode. Please use another login option. Such as Google.", "This file is not a valid JSON file.": "This file is not a valid JSON file.", "This image will be used as placeholder for lazy load images. The best size for this image is 250x250px.": "This image will be used as placeholder for lazy load images. The best size for this image is 250x250px.", "This product is not available.": "This product is not available.", "This product is out of stock or not exists!": "This product is out of stock or not exists!", "Time": "Time", "Time end": "Time end", "Title": "Title", "Title text color": "Title text color", "To track your order please enter your Order ID in the box below and press the \"Track\" button. This was given to you on your receipt and in the confirmation email you should have received.": "To track your order please enter your Order ID in the box below and press the \"Track\" button. This was given to you on your receipt and in the confirmation email you should have received.", "Top": "Top", "Top rated": "Top rated", "Total": "Total", "Total Amount": "Total Amount", "Total:": "Total:", "Track": "Track", "Track Now": "Track Now", "Track Your Order": "Track Your Order", "Track your recent orders and order history": "Track your recent orders and order history", "Tracking ID": "Tracking ID", "Tracking Link": "Tracking Link", "Transaction is already successfully completed!": "Transaction is already successfully completed!", "Transaction is successfully completed!": "Transaction is successfully completed!", "Trending": "Trending", "Type": "Type", "URL": "URL", "URL (optional)": "URL (optional)", "Unable to download files": "Unable to download files", "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.": "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.", "Unit Price": "Unit Price", "Unknown": "Unknown", "Unsubscribe to newsletter successfully": "Unsubscribe to newsletter successfully", "Update": "Update", "Update :name": "Update :name", "Update cart successfully!": "Update cart successfully!", "Update profile successfully!": "Update profile successfully!", "Update return order status to: :status": "Update return order status to: :status", "Update your account profile information and email address.": "Update your account profile information and email address.", "Update your shipping and billing addresses": "Update your shipping and billing addresses", "Updated avatar successfully!": "Updated avatar successfully!", "Upload": "Upload", "Upload Service Account JSON File": "Upload Service Account JSON File", "Upload photos": "Upload photos", "Uploaded proof successfully": "Uploaded proof successfully", "Use this address as default.": "Use this address as default.", "Using coupon code": "Using coupon code", "Validation Fail!": "Validation Fail!", "Variables": "Variables", "Vendor:": "Vendor:", "Vertical": "Vertical", "View": "View", "View Cart": "View Cart", "View Details": "View Details", "View Orders": "View Orders", "View Receipt:": "View Receipt:", "View all results": "View all results", "View full details": "View full details", "Waiting for approval": "Waiting for approval", "Waiting for your review": "Waiting for your review", "Warning": "Warning", "Warning: This product is on backorder and may take longer to ship.": "Warning: This product is on backorder and may take longer to ship.", "We have sent you an email to verify your email. Please check and confirm your email address!": "We have sent you an email to verify your email. Please check and confirm your email address!", "We sent you another confirmation email. You should receive it shortly.": "We sent you another confirmation email. You should receive it shortly.", "We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently and all your data will be lost.": "We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently and all your data will be lost.", "Webhook Secret": "Webhook Secret", "Welcome back, <strong>:name</strong>!": "Welcome back, <strong>:name</strong>!", "What Are You Looking For?": "What Are You Looking For?", "WhatsApp": "WhatsApp", "When a payment fails": "When a payment fails", "When a payment is authorized": "When a payment is authorized", "When a payment is captured": "When a payment is captured", "When a payment is pending": "When a payment is pending", "When an order is paid": "When an order is paid", "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.": "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.", "Widget call to action contact on footer section.": "Widget call to action contact on footer section.", "Widget display Download apps.": "Widget display Download apps.", "Widget display blog categories": "Widget display blog categories", "Widget display site information": "Widget display site information", "Widgets in blog page": "Widgets in blog page", "Widgets in bottom footer of page": "Widgets in bottom footer of page", "Widgets in footer of page": "Widgets in footer of page", "Widgets in middle footer of page": "Widgets in middle footer of page", "Widgets in products listing page": "Widgets in products listing page", "Widgets in the product detail page": "Widgets in the product detail page", "Width": "<PERSON><PERSON><PERSON>", "Wishlist": "Wishlist", "Wooden": "Wooden", "Wooden (random product)": "<PERSON><PERSON> (random product)", "Wrapper text into <code>:tag</code> tag to make it highlight.": "Wrapper text into <code>:tag</code> tag to make it highlight.", "Write your review": "Write your review", "X (Twitter)": "X (Twitter)", "Yes": "Yes", "Yes, turn off": "Yes, turn off", "You can create your app in :link": "You can create your app in :link", "You can get fan page ID using this site :link": "You can get fan page ID using this site :link", "You can now download it by clicking the links below": "You can now download it by clicking the links below", "You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.": "You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.", "You can upload up to :total photos, each photo maximum size is :max MB.": "You can upload up to :total photos, each photo maximum size is :max MB.", "You can upload up to :total photos, each photo maximum size is :max kilobytes": "You can upload up to :total photos, each photo maximum size is :max kilobytes", "You cannot cancel this order": "You cannot cancel this order", "You cannot confirm delivery for this order": "You cannot confirm delivery for this order", "You cannot return this order": "You cannot return this order", "You do not have permission to delete this review.": "You do not have permission to delete this review.", "You have a coupon code?": "You have a coupon code?", "You have created a payment #:charge_id via :channel :time : :amount": "You have created a payment #:charge_id via :channel :time : :amount", "You have not placed any order return requests yet.": "You have not placed any order return requests yet.", "You have not placed any orders yet.": "You have not placed any orders yet.", "You have not purchased any digital products yet.": "You have not purchased any digital products yet.", "You have recovered from previous orders!": "You have recovered from previous orders!", "You have reviewed this product already!": "You have reviewed this product already!", "You have uploaded a copy of your payment proof.": "You have uploaded a copy of your payment proof.", "You haven't placed any orders yet. Browse our products and find something you love!": "You haven't placed any orders yet. Browse our products and find something you love!", "You must agree to the terms and conditions and privacy policy.": "You must agree to the terms and conditions and privacy policy.", "You must agree to the terms and policy.": "You must agree to the terms and policy.", "You must buy at least 1 item": "You must buy at least 1 item", "You need to add :quantity more items of :product to place your order.": "You need to add :quantity more items of :product to place your order.", "You need to add :quantity more items to place your order.": "You need to add :quantity more items to place your order. ", "You successfully confirmed your email address.": "You successfully confirmed your email address.", "You will be redirected to :name to complete the payment.": "You will be redirected to :name to complete the payment.", "YouTube URL": "YouTube URL", "YouTube video": "YouTube video", "YouTube, Vimeo, TikTok, ...": "YouTube, <PERSON><PERSON>o, Tik<PERSON>ok, ...", "Your Address": "Your Address", "Your Addresses": "Your Addresses", "Your Cart": "Your Cart", "Your Email": "Your Email", "Your Message": "Your Message", "Your Name": "Your Name", "Your Phone": "Your Phone", "Your Review": "Your Review", "Your Reviews": "Your Reviews", "Your account has been locked, please contact the administrator.": "Your account has been locked, please contact the administrator.", "Your asset files have been published successfully.": "Your asset files have been published successfully.", "Your cart is empty": "Your cart is empty", "Your cart is empty!": "Your cart is empty!", "Your compare list is empty": "Your compare list is empty", "Your email": "Your email", "Your email address will not be published. Required fields are marked *": "Your email address will not be published. Required fields are marked *", "Your email does not exist in the system or you have unsubscribed already!": "Your email does not exist in the system or you have unsubscribed already!", "Your email is in blacklist. Please use another email address.": "Your email is in blacklist. Please use another email address.", "Your full name": "Your full name", "Your message contains blacklist words: \":words\".": "Your message contains blacklist words: \":words\".", "Your order is successfully placed": "Your order is successfully placed", "Your personal data will be used to support your experience throughout this website, to manage access to your account.": "Your personal data will be used to support your experience throughout this website, to manage access to your account.", "Your rating:": "Your rating:", "Your shopping cart has digital product(s), so you need to sign in to continue!": "Your shopping cart has digital product(s), so you need to sign in to continue!", "Your system has been cleaned up successfully.": "Your system has been cleaned up successfully.", "Your wishlist list is empty": "Your wishlist list is empty", "Zip code": "Zip code", "Zipcode": "Zipcode", "billion": "billion", "centimeters": "centimeters", "for all orders": "for all orders", "for all product in collection :collections": "for all product in collection :collections", "for all products in category :categories": "for all products in category :categories", "for all products in collection :collections": "for all products in collection :collections", "for all products in order": "for all products in order", "for customer(s) :customers": "for customer(s) :customers", "for order with amount from :price": "for order with amount from :price", "for product variant(s) :variants": "for product variant(s) :variants", "for product(s) :products": "for product(s) :products", "grams": "grams", "here": "here", "iOS image": "iOS image", "iOS link": "iOS link", "item(s)": "item(s)", "kilograms": "kilograms", "limited to use coupon code per customer. This coupon can only be used once per customer!": "limited to use coupon code per customer. This coupon can only be used once per customer!", "meters": "meters", "million": "million", "times": "times", "when shipping fee less than or equal :amount": "when shipping fee less than or equal :amount", "| (pipe)": "| (pipe)", "✅ Purchased :time": "✅ Purchased :time"}