<?php

return [
    'name' => 'Discounts',
    'create' => 'Create discount',
    'edit' => 'Edit discount',
    'invalid_coupon' => 'This coupon is invalid or expired!',
    'cannot_use_same_time_with_other_discount_program' => 'Cannot use this coupon in the same time with other discount program!',
    'not_used' => 'This coupon is not used yet!',
    'detail' => 'Detail',
    'used' => 'Used',
    'intro' => [
        'title' => 'Manage discount/coupon code',
        'description' => 'Create coupon code or promotion for your products.',
        'button_text' => 'Create discount',
    ],
    'expired' => 'Expired',
    'discount_promotion' => 'Discount promotion',
    'can' => 'can',
    'cannot' => 'cannot',
    'use_with_promotion' => 'be used with promotion',
    'create_discount_validate_title_required_if' => 'Please enter the name of the promotion',
    'create_discount_validate_code_required_if' => 'Please enter the promotion code',
    'create_discount_validate_value_required' => 'Amount must be greater than 0',
    'create_discount_validate_target_required' => 'No object selected for promotion',
    'enums' => [
        'type-options' => [
            'amount' => 'Amount - Fixed',
            'percentage' => 'Discount %',
            'shipping' => 'Free shipping',
            'same-price' => 'Same price',
        ],
        'types' => [
            'coupon' => 'Coupon',
            'promotion' => 'Promotion',
        ],
        'targets' => [
            'all-orders' => 'All orders',
            'customer' => 'Customer',
            'amount-minimum-order' => 'Minimum order amount',
            'product-variant' => 'Product variant',
            'group-products' => 'Product collections',
            'specific-product' => 'Specific product',
            'products-by-category' => 'Products by category',
        ],
    ],
    'discount' => 'Discount',
    'create_coupon_code' => 'Create coupon code',
    'create_discount_promotion' => 'Create discount promotion',
    'generate_coupon_code' => 'Generate coupon code',
    'customers_will_enter_this_coupon_code_when_they_checkout' => 'Customers will enter this coupon code when they checkout',
    'select_type_of_discount' => 'Select type of discount',
    'coupon_code' => 'Coupon code',
    'promotion' => 'Promotion',
    'can_be_used_with_promotion' => 'Can be used with promotion?',
    'can_be_used_with_flash_sale' => 'Can be used with flash sale?',
    'can_be_used_with_flash_sale_help' => 'Allows customers to apply the coupon to items already on flash sale, enabling combined discounts.',
    'unlimited_coupon' => 'Unlimited coupon?',
    'enter_number' => 'Enter number',
    'coupon_type' => 'Coupon type',
    'percentage_discount' => 'Percentage discount (%)',
    'free_shipping' => 'Free shipping',
    'same_price' => 'Same price',
    'apply_for' => 'apply for',
    'all_orders' => 'All orders',
    'order_amount_from' => 'Order amount from',
    'product_collection' => 'Product collection',
    'product_category' => 'Product category',
    'product' => 'Product',
    'customer' => 'Customer',
    'variant' => 'Variant',
    'search_product' => 'Search product',
    'no_products_found' => 'No products found!',
    'search_customer' => 'Search customer',
    'no_customer_found' => 'No customer found!',
    'one_time_per_order' => 'One time per order',
    'one_time_per_product_in_cart' => 'One time per product in cart',
    'number_of_products' => 'Number of products required to apply',
    'selected_products' => 'Selected products',
    'selected_customers' => 'Selected customers',
    'time' => 'Time',
    'start_date' => 'Start date',
    'end_date' => 'End date',
    'never_expired' => 'Never expired?',
    'save' => 'Save',
    'enter_promotion_name' => 'Enter promotion name',
    'enter_coupon_name' => 'Enter coupon name',
    'cancel' => 'Cancel',
    'is' => 'Is',
    'when_shipping_fee_less_than' => 'when shipping fee less than',
    'minimum_order_amount_error' => 'You are under :minimum_amount to apply the coupon, you must add :add_more more items to your cart',
    'once_per_customer' => 'Once per customer',
    'you_need_login_to_use_coupon_code' => 'You need to login to use this coupon code',
    'you_used_coupon_code' => 'You have already used this coupon code',
    'customer_used_coupon_code' => 'Customers who have used this discount code',
    'apply_via_url' => 'Apply via URL?',
    'apply_via_url_description' => 'This setting will apply coupon code when customers access the URL with the parameter "?coupon=code".',
    'display_at_checkout' => 'Display coupon code at the checkout page?',
    'display_at_checkout_description' => 'The list of coupon codes will be displayed at the checkout page and customers can choose to apply.',
    'description' => 'Description',
    'description_placeholder' => 'Short description about the discount program',
    'cannot_use_same_time_with_flash_sale' => 'This coupon cannot be used with flash sale items like :product_name. Please remove the item from your cart or choose a different coupon.',
];
