<?php

return [
    'name' => 'Shipping Rules',
    'shipping' => 'Shipping',
    'title' => 'Title',
    'amount' => 'Amount',
    'enable' => 'Enable',
    'enabled' => 'Enabled',
    'disable' => 'Disable',
    'disabled' => 'Disabled',
    'create_shipping' => 'Create a shipping',
    'edit_shipping' => 'Edit shipping :code',
    'status' => 'Status',
    'shipping_methods' => 'Shipping methods',
    'create_shipping_method' => 'Create shipping method',
    'edit_shipping_method' => 'Edit shipping method',
    'add_shipping_region' => 'Add shipping region',
    'country' => 'Country',
    'state' => 'State',
    'city' => 'City',
    'address' => 'Address',
    'phone' => 'Phone',
    'email' => 'Email',
    'zip_code' => 'Zip code',
    'methods' => [
        'default' => 'Default',
    ],
    'statuses' => [
        'not_approved' => 'Not approved',
        'approved' => 'Approved',
        'pending' => 'Pending',
        'arrange_shipment' => 'Arrange shipment',
        'ready_to_be_shipped_out' => 'Ready to be shipped out',
        'picking' => 'Picking',
        'delay_picking' => 'Delay picking',
        'picked' => 'Picked',
        'not_picked' => 'Not picked',
        'delivering' => 'Delivering',
        'delivered' => 'Delivered',
        'not_delivered' => 'Not delivered',
        'audited' => 'Audited',
        'canceled' => 'Canceled',
    ],
    'cod_statuses' => [
        'pending' => 'Pending',
        'completed' => 'Completed',
    ],
    'delete' => 'Delete',
    'shipping_rules' => 'Shipping Rules',
    'shipping_rules_description' => 'Rules to calculate shipping fee.',
    'select_country' => 'Select country',
    'add_shipping_rule' => 'Add shipping rule',
    'delete_shipping_rate' => 'Delete shipping rate for area',
    'delete_shipping_rate_confirmation' => 'Are you sure you want to delete <strong class="region-price-item-label"></strong> from this shipping area?',
    'delete_shipping_area' => 'Delete shipping area',
    'delete_shipping_area_confirmation' => 'Are you sure you want to delete shipping area <strong class="region-item-label"></strong>?',
    'add_shipping_fee_for_area' => 'Add shipping fee for area',
    'confirm' => 'Confirm',
    'save' => 'Save',
    'greater_than' => 'Greater than',
    'type' => 'Type',
    'shipping_rule_name' => 'Name of shipping rule',
    'shipping_fee' => 'Shipping fee',
    'cancel' => 'Cancel',
    'based_on_weight' => 'Based on product\'s weight (:unit)',
    'based_on_price' => 'Based on product\'s price',
    'shipment_canceled' => 'Shipment was canceled',
    'at' => 'At',
    'cash_on_delivery' => 'Cash on delivery (COD)',
    'update_shipping_status' => 'Update shipping status',
    'update_cod_status' => 'Update COD status',
    'history' => 'History',
    'shipment_information' => 'Shipment information',
    'order_number' => 'Order number',
    'shipping_method' => 'Shipping method',
    'select_shipping_method' => 'Select shipping method',
    'cod_status' => 'COD status',
    'shipping_status' => 'Shipping status',
    'customer_information' => 'Customer information',
    'sku' => 'SKU',
    'change_status_confirm_title' => 'Confirm <span class="shipment-status-label"></span> ?',
    'change_status_confirm_description' => 'Are you sure you want to confirm <span class="shipment-status-label"></span> for this shipment?',
    'accept' => 'Accept',
    'weight_unit' => 'Weight (:unit)',
    'updated_at' => 'Last Update',
    'cod_amount' => 'Cash on delivery amount (COD)',
    'cancel_shipping' => 'Cancel shipping',
    'shipping_address' => 'Shipping address',
    'packages' => 'Packages',
    'edit' => 'Edit',
    'fee' => 'Fee',
    'note' => 'Note',
    'finish' => 'Finish',
    'shipping_fee_cod' => 'Shipping fee/COD',
    'send_confirmation_email_to_customer' => 'Send confirmation email to customer',
    'form_name' => 'Name',
    'changed_shipping_status' => 'Changed status of shipping to: :status. Updated by: %user_name%',
    'order_confirmed_by' => 'Order confirmed by %user_name%',
    'shipping_canceled_by' => 'Shipping is cancelled by %user_name%',
    'update_shipping_status_success' => 'Update shipping status successfully!',
    'update_cod_status_success' => 'Updated COD status of shipping successfully!',
    'updated_cod_status_by' => 'Updated COD status to :status . Updated by: %user_name%',
    'all' => 'All',
    'all_countries' => 'All countries',
    'error_when_adding_new_region' => 'There is an error when adding new region!',
    'delivery' => 'Delivery',
    'adjustment_price_of' => 'Adjustment price of :key',
    'warehouse' => 'Warehouse',
    'delivery_note' => 'Delivery note',
    'customer_note' => 'Customer note',
    'shipments' => 'Shipments',
    'order_id' => 'Order ID',
    'shipment_id' => 'Shipment ID',
    'not_available' => 'Not available',
    'shipping_amount' => 'Shipping Amount',
    'additional_shipment_information' => 'Additional shipment information',
    'shipping_company_name' => 'Shipping Company Name',
    'shipping_company_name_placeholder' => 'Ex: DHL, AliExpress...',
    'tracking_id' => 'Tracking ID',
    'tracking_id_placeholder' => 'Ex: JJD0099999999',
    'tracking_link' => 'Tracking Link',
    'tracking_link_placeholder' => 'Ex: https://mydhl.express.dhl/us/en/tracking.html#/track-by-reference',
    'estimate_date_shipped' => 'Estimate Date Shipped',
    'date_shipped' => 'Date Shipped',
    'add_note' => 'Add note...',
    'view_order' => 'View Order :order_id',
    'rule' => [
        'enum_types' => [
            'based_on_weight' => 'Based on order\'s total weight (:unit)',
            'based_on_price' => 'Based on order\'s total amount',
            'based_on_zipcode' => 'Based on zipcode',
            'based_on_location' => 'Based on location',
            'unavailable' => 'Unavailable',
        ],
        'item' => [
            'name' => 'Shipping Rule Items',
            'edit' => 'Edit item',
            'create' => 'Create new item',
            'delete' => 'Delete shipping rule item',
            'confirmation' => 'Are you sure you want to delete shipping rule item <strong class="item-label"></strong>?',
            'load_data_table' => 'Load data table (:total)',
            'tables' => [
                'shipping_rule' => 'Shipping rule',
                'country' => 'Country',
                'state' => 'State',
                'city' => 'City',
                'zip_code' => 'Zip code',
                'adjustment_price' => 'Adjustment price',
                'is_enabled' => 'Is enabled?',
            ],
            'forms' => [
                'country' => 'Country',
                'country_placeholder' => 'Country',
                'state' => 'State',
                'state_placeholder' => 'State',
                'city' => 'City',
                'city_placeholder' => 'City',
                'shipping_rule' => 'Shipping rule',
                'zip_code' => 'Zip code',
                'zip_code_placeholder' => 'Zip code',
                'adjustment_price' => 'Adjustment price',
                'adjustment_price_placeholder' => 'Adjustment price',
                'is_enabled' => 'Is enabled?',
                'no_shipping_rule' => 'No shipping rule',
                'adjustment_price_helper' => 'To subtract from the price, simply utilize a negative number. e.g. -10',
            ],
            'bulk-import' => [
                'menu' => 'Bulk import Shipping Rule Items',
                'greater_than_or_equal' => 'Only numbers or decimals are accepted and greater than or equal to :min.',
                'less_than_or_equal' => 'Only numbers or decimals are accepted and less than or equal to :max.',
                'between' => 'Only numbers or decimals are accepted and between :min and :max.',
                'overwrite' => 'Overwrite',
                'add_new' => 'Add new',
                'skip' => 'Skip',
            ],
        ],
        'select_type' => 'Select type',
        'cannot_create_rule_type_for_this_location' => 'Cannot create rule type ":type" on this location!',
    ],
    'empty_shipping_options' => [
        'title' => 'No shipping options',
        'subtitle' => 'Click on add country from the left side to add new shipping options.',
    ],
    'shipping_based_on_location_instruction' => 'If you want to set shipping fee based on location, you need to enable ":link_text" in Settings -> Checkout and import location data in Tools -> Import/Export Data.',
    'shipping_based_on_zip_code_instruction' => 'If you want to set shipping fee based on zip code, you need to enable ":link_text" in Settings -> Checkout and set zip code for store address.',
    'shipping_label' => [
        'name' => 'Shipping label',
        'print' => 'Print',
        'print_shipping_label' => 'Print shipping label',
        'sender' => 'Sender',
        'order_date' => 'Order date',
        'scan_qr_code' => 'Scan QR code to track your shipment',
    ],
    'customer_confirmed_delivery_at' => 'Customer confirmed delivery at',
];
