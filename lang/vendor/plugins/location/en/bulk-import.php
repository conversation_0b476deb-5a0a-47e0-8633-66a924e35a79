<?php

return [
    'name' => 'Location Importer',
    'imported_successfully' => 'Your locations have been imported successfully!',
    'choose_country' => 'Choose country...',
    'import_failed_description' => 'Import failed, please check the errors below!',
    'available_enable_multi_language' => 'Only available when you enable "Is Multi Language?"',
    'import_available_data_help' => "Countries are excluded from the list as they're already in the system. To re-import any, delete them from the :link and retry.",
    'import_available_data' => 'Import available data',
    'available_data_help' => 'Location data is sourced from our GitHub repository at :link. To contribute and have your updates reflected here, please submit a pull request.',
    'import' => 'Import',
    'upload_file_placeholder' => 'Drag and drop file here or click to upload',
    'skip_existing_records' => 'Skip existing records',
    'skip_existing_records_description' => 'It will verify whether the country, state, or city already exists in the database by name and skip importing any records that are already present.',
    'cant_download_location_at_this_time' => 'We cannot download the location at this time. Please try again later.',
];
