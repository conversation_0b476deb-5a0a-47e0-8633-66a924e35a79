<?php

return [
    'setting' => [
        'title' => 'Sale popup',
        'description' => 'Settings for popup sale',
    ],
    'enable' => 'Enable?',
    'select_pages_to_display' => 'Select pages to display',
    'purchased_text' => 'Purchased text',
    'verified_text' => 'Verified text',
    'quick_view_text' => 'Quick view text',
    'list_users_purchased' => 'List of users purchased:',
    'list_sale_time' => 'List of sale time',
    'limit_products' => 'Limit Products',
    'show_close_button' => 'Show close button?',
    'show_quick_view_button' => 'Show quick view button?',
    'show_time_ago_suggest' => 'Show time ago in suggest?',
    'show_verified' => 'Show Verified?',
    'show_on_mobile' => 'Show on mobile devices?',
    'user_separator' => "Separate with '|'. If you not want use list user you can write a text. eg:'someone'.",
    'time_separator' => "Separate with '|'.",
    'enable_helper' => 'Enable sale popup feature on your site.',
    'purchased_text_helper' => 'Text displayed after the user name, e.g. "purchased".',
    'verified_text_helper' => 'Text displayed for verification badge.',
    'quick_view_text_helper' => 'Text displayed on quick view button.',
    'limit_products_helper' => 'Maximum number of products to display in the popup.',
    'show_verified_helper' => 'Display verification badge next to user name.',
    'show_close_button_helper' => 'Allow users to close the popup.',
    'show_quick_view_button_helper' => 'Display quick view button on the popup.',
    'show_time_ago_suggest_helper' => 'Show how long ago the purchase was made.',
    'show_on_mobile_helper' => 'Display sale popup on mobile devices. If disabled, popup will only show on desktop.',
    'collection_id_helper' => 'Select which product collection to display in the popup.',
    'featured_products' => 'Featured products',
    'load_products_from' => 'Load products from',
    'display_pages' => [
        'homepage' => 'Homepage',
        'product_detail' => 'Product detail',
        'product_listing' => 'Product listing',
        'cart' => 'Cart',
    ],
];
