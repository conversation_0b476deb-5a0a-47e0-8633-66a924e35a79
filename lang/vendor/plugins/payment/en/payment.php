<?php

return [
    'payments' => 'Payments',
    'checkout_success' => 'Checkout successfully!',
    'view_payment' => 'View payment #',
    'charge_id' => 'Charge ID',
    'amount' => 'Amount',
    'currency' => 'Currency',
    'user' => 'User',
    'stripe' => 'Stripe',
    'paypal' => 'PayPal',
    'action' => 'Action',
    'payment_via_card' => 'Payment via card',
    'card_number' => 'Card number',
    'full_name' => 'Full name',
    'payment_via_paypal' => 'Fast and safe online payment via PayPal.',
    'mm_yy' => 'MM/YY',
    'cvc' => 'CVC',
    'details' => 'Details',
    'payer_name' => 'Payer Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'country' => 'Country',
    'shipping_address' => 'Shipping Address',
    'payment_details' => 'Payment Details',
    'card' => 'Card',
    'address' => 'Address',
    'could_not_get_stripe_token' => 'Could not get Stripe token to make a charge.',
    'payment_id' => 'Payment ID',
    'payment_methods' => 'Payment methods',
    'transactions' => 'Transactions',
    'payment_methods_description' => 'Setup payment methods for website',
    'paypal_description' => 'Customer can buy product and pay directly via PayPal',
    'use' => 'Use',
    'stripe_description' => 'Customer can buy product and pay directly using Visa, Credit card via Stripe',
    'edit' => 'Edit',
    'settings' => 'Settings',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'update' => 'Update',
    'configuration_instruction' => 'Configuration instruction for :name',
    'configuration_requirement' => 'To use :name, you need',
    'service_registration' => 'Register with :name',
    'after_service_registration_msg' => 'After registration at :name, you will have Client ID, Client Secret',
    'enter_client_id_and_secret' => 'Enter Client ID, Secret into the box in right hand',
    'method_name' => 'Method name',
    'method_logo' => 'Method logo',
    'please_provide_information' => 'Please provide information',
    'client_id' => 'Client ID',
    'client_secret' => 'Client Secret',
    'secret' => 'Secret',
    'stripe_key' => 'Stripe Public Key',
    'stripe_secret' => 'Stripe Private Key',
    'stripe_after_service_registration_msg' => 'After registration at :name, you will have Public, Secret keys',
    'stripe_enter_client_id_and_secret' => 'Enter Public, Secret keys into the box in right hand',
    'pay_online_via' => 'Pay online via :name',
    'sandbox_mode' => 'Sandbox mode',
    'live_mode' => 'Live mode',
    'deactivate_payment_method' => 'Deactivate payment method',
    'deactivate_payment_method_description' => 'Do you really want to deactivate this payment method?',
    'agree' => 'Agree',
    'name' => 'Payments',
    'create' => 'New payment',
    'go_back' => 'Go back',
    'information' => 'Information',
    'methods' => [
        'paypal' => 'PayPal',
        'cod' => 'Cash on delivery (COD)',
        'bank_transfer' => 'Bank transfer',
    ],
    'statuses' => [
        'pending' => 'Pending',
        'completed' => 'Completed',
        'refunding' => 'Refunding',
        'refunded' => 'Refunded',
        'fraud' => 'Fraud',
        'failed' => 'Failed',
        'canceled' => 'Canceled',
    ],
    'payment_methods_instruction' => 'Guide customers to pay directly. You can choose to pay by delivery or bank transfer',
    'payment_method_description' => 'Payment guide - (Displayed on the notice of successful purchase and payment page)',
    'payment_via_cod' => 'Cash on delivery (COD)',
    'payment_via_bank_transfer' => 'Bank transfer',
    'payment_pending' => 'Checkout successfully. Your payment is pending and will be checked by our staff.',
    'created_at' => 'Created At',
    'payment_channel' => 'Payment Channel',
    'total' => 'Total',
    'status' => 'Status',
    'default_payment_method' => 'Default payment method',
    'available_countries' => 'Available countries',
    'available_countries_help' => 'Choose the countries where this payment method is available.',
    'all_countries_checkbox' => 'All',
    'turn_off_success' => 'Turn off payment method successfully!',
    'saved_payment_settings_success' => 'Saved payment settings successfully!',
    'payment_name' => 'Name',
    'payment_not_found' => 'Payment not found!',
    'refunds' => [
        'title' => 'Refunds',
        'id' => 'ID',
        'breakdowns' => 'Breakdowns',
        'gross_amount' => 'Gross amount',
        'paypal_fee' => 'PayPal fee',
        'net_amount' => 'Net amount',
        'total_refunded_amount' => 'Total refunded amount',
        'create_time' => 'Create time',
        'update_time' => 'Update time',
        'status' => 'Status',
        'description' => 'Description',
        'refunded_at' => 'Refunded at',
        'error_message' => 'Error message',
    ],
    'view_response_source' => 'View response source',
    'status_is_not_completed' => 'Status is not COMPLETED',
    'cannot_found_capture_id' => 'Can not found capture id with payment detail',
    'amount_refunded' => 'Amount refunded',
    'amount_remaining' => 'Amount remaining',
    'paid_at' => 'Paid At',
    'invalid_settings' => 'Settings for :name are invalid!',
    'view_transaction' => 'Transaction ":charge_id"',
    'payment_description' => 'Pay for your order #:order_id at :site_url',
    'processing_fee' => 'Processing fee (Optional)',
    'fee_helper' => 'Extra fee will be charged when customer selects this payment method. Enter 0 for no fee.',
    'payment_fee' => 'Payment fee',
    'payment_log' => [
        'name' => 'Payment Logs',
        'view' => 'View Payment Log #:id',
        'ip_address' => 'IP Address',
        'type' => 'Type',
        'request' => 'Request',
        'response' => 'Response',
    ],
    'fee_types' => [
        'fixed' => 'Fixed',
        'percentage' => 'Percentage',
    ],
    'fee_type' => 'Processing fee type (Optional)',
    'fee_type_helper' => 'Select how the payment fee should be calculated: as a fixed amount or a percentage of the order total. If you choose a fixed amount, it will be based on the default currency (:currency).',
];
