<?php

return [
    'search' => 'Search...',
    'enabled' => 'Enabled',
    'deactivated' => 'Deactivated',
    'activated' => 'Activated',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'author' => 'By',
    'update_plugin_status_success' => 'Update plugin successfully',
    'plugins' => 'Plugins',
    'installed_plugins' => 'Installed Plugins',
    'add_new_plugin' => 'Add New Plugin',
    'missing_required_plugins' => 'Please activate plugin(s): :plugins before activating this plugin!',
    'remove' => 'Remove',
    'remove_plugin_success' => 'Remove plugin successfully!',
    'remove_plugin' => 'Remove plugin',
    'remove_plugin_confirm_message' => 'Do you really want to remove this plugin?',
    'remove_plugin_confirm_yes' => 'Yes, remove it!',
    'total_plugins' => 'Total plugins',
    'invalid_plugin' => 'This plugin is not a valid plugin, please check it again!',
    'version' => 'Version',
    'invalid_json' => 'Invalid plugin.json!',
    'activate_success' => 'Activate plugin successfully!',
    'activated_already' => 'This plugin is activated already!',
    'plugin_not_exist' => 'This plugin is not exists.',
    'missing_json_file' => 'Missing file plugin.json!',
    'plugin_invalid' => 'Plugin is valid!',
    'published_assets_success' => 'Publish assets for plugin :name successfully!',
    'plugin_removed' => 'Plugin has been removed!',
    'deactivated_success' => 'Deactivate plugin successfully!',
    'deactivated_already' => 'This plugin is deactivated already!',
    'folder_is_not_writeable' => 'Cannot write files! Folder :name is not writable. Please chmod to make it writable!',
    'plugin_is_not_ready' => 'Plugin :name is not ready to use',
    'plugins_installed' => 'Installed Plugins',
    'plugins_add_new' => 'Add new',
    'update' => 'Update',
    'requirement_not_met' => 'Plugin :plugin requires plugin(s): :required_plugins. Do you want to install them and activate :plugin now?',
    'install' => 'Install now',
    'install_plugin' => 'Install Plugin',
    'minimum_core_version_not_met' => 'Plugin :plugin requires :minimum_core_version version of core, but your core version is :current_core_version. Please upgrade your core to use this plugin!',
    'required_by_other_plugins' => 'Cannot deactivate plugin :plugin because it is required by other plugin(s): :required_by',
    'enums' => [
        'plugin_filter_status' => [
            'all' => 'All',
            'activated' => 'Activated',
            'not-activated' => 'Not Activated',
            'updates-available' => 'Updates Available',
        ],
    ],
];
