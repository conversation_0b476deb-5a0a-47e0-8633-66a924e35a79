/*!
* inputmask.phone.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(e){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],e):"object"==typeof exports?module.exports=e(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):e(window.dependencyLib||jQuery,window.Inputmask)}(function(e,r){function n(e,r){var n=(e.mask||e).replace(/#/g,"0").replace(/\)/,"0").replace(/[+()#-]/g,""),o=(r.mask||r).replace(/#/g,"0").replace(/\)/,"0").replace(/[+()#-]/g,"");return n.localeCompare(o)}var o=r.prototype.analyseMask;return r.prototype.analyseMask=function(r,n,t){var a={};return t.phoneCodes&&(t.phoneCodes&&t.phoneCodes.length>1e3&&(function e(n,o,t){o=o||"",t=t||a,""!==o&&(t[o]={});for(var p="",u=t[o]||t,i=n.length-1;i>=0;i--)u[p=(r=n[i].mask||n[i]).substr(0,1)]=u[p]||[],u[p].unshift(r.substr(1)),n.splice(i,1);for(var s in u)u[s].length>500&&e(u[s].slice(),s,u)}((r=r.substr(1,r.length-2)).split(t.groupmarker[1]+t.alternatormarker+t.groupmarker[0])),r=function r(n){var o="",a=[];for(var p in n)e.isArray(n[p])?1===n[p].length?a.push(p+n[p]):a.push(p+t.groupmarker[0]+n[p].join(t.groupmarker[1]+t.alternatormarker+t.groupmarker[0])+t.groupmarker[1]):a.push(p+r(n[p]));return 1===a.length?o+=a[0]:o+=t.groupmarker[0]+a.join(t.groupmarker[1]+t.alternatormarker+t.groupmarker[0])+t.groupmarker[1],o}(a)),r=r.replace(/9/g,"\\9")),o.call(this,r,n,t)},r.extendAliases({abstractphone:{groupmarker:["<",">"],countrycode:"",phoneCodes:[],keepStatic:"auto",mask:function(e){return e.definitions={"#":r.prototype.definitions[9]},e.phoneCodes.sort(n)},onBeforeMask:function(e,r){var n=e.replace(/^0{1,2}/,"").replace(/[\s]/g,"");return(n.indexOf(r.countrycode)>1||-1===n.indexOf(r.countrycode))&&(n="+"+r.countrycode+n),n},onUnMask:function(e,r,n){return e.replace(/[()#-]/g,"")},inputmode:"tel"}}),r});