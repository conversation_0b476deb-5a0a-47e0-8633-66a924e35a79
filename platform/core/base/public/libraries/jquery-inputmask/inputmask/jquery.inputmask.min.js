/*!
* jquery.inputmask.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.1-beta.3
*/

!function(t){"function"==typeof define&&define.amd?define(["jquery","./inputmask"],t):"object"==typeof exports?module.exports=t(require("jquery"),require("./inputmask")):t(jQuery,window.Inputmask)}(function(t,e){return void 0===t.fn.inputmask&&(t.fn.inputmask=function(i,n){var s,a=this[0];if(void 0===n&&(n={}),"string"==typeof i)switch(i){case"unmaskedvalue":return a&&a.inputmask?a.inputmask.unmaskedvalue():t(a).val();case"remove":return this.each(function(){this.inputmask&&this.inputmask.remove()});case"getemptymask":return a&&a.inputmask?a.inputmask.getemptymask():"";case"hasMaskedValue":return!(!a||!a.inputmask)&&a.inputmask.hasMaskedValue();case"isComplete":return!a||!a.inputmask||a.inputmask.isComplete();case"getmetadata":return a&&a.inputmask?a.inputmask.getmetadata():void 0;case"setvalue":e.setValue(a,n);break;case"option":if("string"!=typeof n)return this.each(function(){if(void 0!==this.inputmask)return this.inputmask.option(n)});if(a&&void 0!==a.inputmask)return a.inputmask.option(n);break;default:return n.alias=i,s=new e(n),this.each(function(){s.mask(this)})}else{if("object"==typeof i)return s=new e(i),void 0===i.mask&&void 0===i.alias?this.each(function(){if(void 0!==this.inputmask)return this.inputmask.option(i);s.mask(this)}):this.each(function(){s.mask(this)});if(void 0===i)return this.each(function(){(s=new e(n)).mask(this)})}}),t.fn.inputmask});