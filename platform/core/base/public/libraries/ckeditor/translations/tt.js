!function(e){const t=e.tt=e.tt||{};t.dictionary=Object.assign(t.dictionary||{},{"%0 of %1":"",Accept:"","Align cell text to the bottom":"","Align cell text to the center":"","Align cell text to the left":"","Align cell text to the middle":"","Align cell text to the right":"","Align cell text to the top":"","Align table to the left":"","Align table to the right":"",Alignment:"",Aquamarine:"Аквамарин",Background:"",Black:"Кара",Blue:"Зәңгәр",Bold:"Калын",Border:"","Break text":"","Bulleted List":"",Cancel:"Баш тарт","Cannot upload file:":"","Caption for image: %0":"","Caption for the image":"","Cell properties":"","Center table":"","Centered image":"","Change image text alternative":"",Clear:"","Click to edit block":"",Code:"Код",Color:"Төс","Color picker":"",Column:"",Dashed:"","Delete column":"","Delete row":"","Dim grey":"",Dimensions:"",Dotted:"",Double:"",Downloadable:"","Drag to move":"","Dropdown toolbar":"","Edit block":"","Edit link":"","Editor block content toolbar":"","Editor contextual toolbar":"","Editor editing area: %0":"","Editor toolbar":"","Enter image caption":"",Find:"Таб","Find and replace":"","Find in text…":"Текстта таб...","Full size image":"",Green:"Яшел",Grey:"Соры",Groove:"","Header column":"","Header row":"",Height:"",HEX:"","Horizontal text alignment toolbar":"","Image resize list":"","Image toolbar":"","image widget":"","In line":"",Insert:"","Insert column left":"","Insert column right":"","Insert image":"","Insert image via URL":"","Insert image with file manager":"","Insert row above":"","Insert row below":"","Insert table":"","Insert with file manager":"",Inset:"",Italic:"","Justify cell text":"","Left aligned image":"","Light blue":"Ачык зәңгәр","Light green":"Ачык яшел","Light grey":"Ачык соры",Link:"Сылтама","Link URL":"","Match case":"","Merge cell down":"","Merge cell left":"","Merge cell right":"","Merge cell up":"","Merge cells":"",Next:"","Next result":"","No results found":"","No searchable items":"",None:"","Numbered List":"","Open in a new tab":"","Open link in new tab":"",Orange:"Кызгылт",Original:"",Outset:"",Padding:"",Previous:"","Previous result":"",Purple:"Шәмәхә",Red:"Кызыл",Redo:"Кабатла","Remove color":"",Replace:"","Replace all":"","Replace from computer":"","Replace image":"","Replace image from computer":"","Replace image with file manager":"","Replace with file manager":"","Replace with…":"","Resize image":"","Resize image to %0":"","Resize image to the original size":"","Restore default":"","Rich Text Editor":"",Ridge:"","Right aligned image":"",Row:"",Save:"Сакла","Select column":"","Select row":"","Show more items":"","Show options":"","Side image":"",Solid:"","Split cell horizontally":"","Split cell vertically":"",Strikethrough:"",Style:"","Table alignment toolbar":"","Table cell text alignment":"","Table properties":"","Table toolbar":"","Text alternative":"","Text to find must not be empty.":"",'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".':"",'The value is invalid. Try "10px" or "2em" or simply "2".':"","This link has no URL":"","Tip: Find some text first in order to replace it.":"","Toggle caption off":"","Toggle caption on":"",Turquoise:"Фервоз",Underline:"",Undo:"",Unlink:"",Update:"Яңарт","Update image URL":"","Upload failed":"","Upload from computer":"","Upload image from computer":"","Vertical text alignment toolbar":"",White:"Ак","Whole words only":"",Width:"","Wrap text":"",Yellow:"Сары"}),t.getPluralForm=function(e){return 0}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));