var paginateSelectClassName="paginate_select",paginateTotalClassName="paginate_total";$.fn.dataTableExt.oPagination.listbox={fnInit:function(a,e,t){var n=document.createElement("select"),i=document.createElement("span"),s=document.createElement("span"),l=a.oLanguage.oPaginate.info||"Page _INPUT_ of _TOTAL_";n.className=paginateSelectClassName,i.className=paginateTotalClassName,""!==a.sTableId&&e.setAttribute("id",a.sTableId+"_paginate"),n.classList.add("form-select"),n.classList.add("form-select-sm"),l=(l=l.replace(/_INPUT_/g,"</span>"+n.outerHTML+"<span>")).replace(/_TOTAL_/g,"</span>"+i.outerHTML),s.innerHTML="<span>"+l,$(s).children().each(function(a,t){e.appendChild(t)}),$(e).find("."+paginateSelectClassName).change(function(e){if(!(""===this.value||this.value.match(/[^0-9]/))){var n=a._iDisplayLength*(this.value-1);if(n>a.fnRecordsDisplay()){a._iDisplayStart=(Math.ceil((a.fnRecordsDisplay()-1)/a._iDisplayLength)-1)*a._iDisplayLength,t(a);return}a._iDisplayStart=n,t(a)}}),$("span",e).bind("mousedown",function(){return!1}),$("span",e).bind("selectstart",function(){return!1})},fnUpdate:function(a,e){if(a.aanFeatures.p){var t=Math.ceil(a.fnRecordsDisplay()/a._iDisplayLength),n=Math.ceil(a._iDisplayStart/a._iDisplayLength)+1,i=a.aanFeatures.p,s=$(i).find("."+paginateSelectClassName);if(s.children("option").length!=t)for(var l=0;l<t;l++){var p=document.createElement("option");p.text=l+1,p.value=l+1,s.append(p)}s.val(n),$(i).find("."+paginateTotalClassName).html(t)}}};
