<?php

namespace Bo<PERSON>ble\ACL\Http\Requests;

use Botble\Base\Rules\EmailRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;

class UpdateProfileRequest extends Request
{
    public function rules(): array
    {
        return [
            'username' => ['required', 'string', 'alpha_dash', 'min:3', 'max:120'],
            'first_name' => ['required', 'string', 'max:60', 'min:2'],
            'last_name' => ['required', 'string', 'max:60', 'min:2'],
            'email' => ['required', 'max:120', 'min:6', new EmailRule()],
        ];
    }
}
