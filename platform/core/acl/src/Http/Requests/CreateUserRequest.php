<?php

namespace Bo<PERSON>ble\ACL\Http\Requests;

use Bo<PERSON>ble\ACL\Models\User;
use Bo<PERSON>ble\Base\Rules\EmailRule;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CreateUserRequest extends Request
{
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:60', 'min:2'],
            'last_name' => ['required', 'string', 'max:60', 'min:2'],
            'email' => [
                'required',
                'min:6',
                'max:120',
                new EmailRule(),
                Rule::unique((new User())->getTable(), 'email'),
            ],
            'password' => ['required', 'string', 'min:6', 'max:120', 'confirmed'],
            'username' => [
                'required',
                'string',
                'alpha_dash',
                'min:3',
                'max:120',
                Rule::unique((new User())->getTable(), 'username'),
            ],
        ];
    }
}
